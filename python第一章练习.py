# pytho学习练习

# a = int(input())
# b = int(input())
# print(a + b)

# list（ 列表）
# a = [1,2,3,4,5,6,7,8,9]
# s = input()
# numbers = s.split()
# print (int (numbers[0]) + int (numbers[1]))

# 打列表中第二个字符的第一个字符
# print(input().split()[2][1])

# 打印三角形
# a = input()
# print("  " + a)
# print(" " + a * 3)
# print (a * 5)

# 计算（a +b）* c的值
# s = input().split()
# a,b,c = int(s[0]),int(s[1]),int(s[2])
# print ((a + b) * c)
#
# 反向输出一个三位数：
# n = input()
# print (n[2] + n[1] + n[0])
#
# 算术运算
# print([] == False)
# print (not[])
# print(0 == False)
# print(1 == True)
# print (not"")
#
# 条件分支语句
# if int(input()) == 6:
#     print("a",end="")
# print("b")
#
# # 温度转换程序
# tmpStr = input("请输入带有符号的温度值：")
# if tmpStr[-1] in ["F","f"]:
#     C = ((float (tmpStr[0:-1])) - 32 ) /1.8
#     print("转换后的温度是" + str(C) + "C")
# elif tmpStr[-1] in ["C","c"]:
#     F = 1.8 * eval(tmpStr[0:-1]) + 32
#     print("转换后的温度是" + str(F) + "F")
# else:
#     print("输入的格式有误")
# """

# help("map")



