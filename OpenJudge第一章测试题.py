# python第一章测试题

# 001:字符菱形
# s = input().strip()
# 定义5行的字符数列表
# chars_per_line = [1, 3, 5, 3, 1]
# for n in chars_per_line:
#     spaces = (5 - n) // 2  # 计算两侧空格数
#     print(' ' * spaces + s * n)

# 002:字符三角形
# a = input()
# print("  " + a)
# print(" " + a + a + a)
# print(a * 5)

# 003:输出第二个整数
# s = input().split()
# print(s[1])

# 004:求三个数的和
# nums = input().split()
# a, b, c = float(nums[0]), float(nums[1]), float(nums[2])
# total = a + b + c
# if total.is_integer():
#     print(f"{total:.1f}")
# else:
#     print(total)

# 005:判断子串
# a = input()
# b = input()
# if a  in b :
#     print("yes")
# else:
#     print("on")

# 006:计算(a+b)*c的值
# s = input().split()
# a,b,c = int(s[0]),int(s[1]),int(s[2])
# print(( a + b) * c)

# 007:反向输出一个三位数
# a = input()
# print(a[2] + a[1] + a[0])

# 008:字符串交换
# a = input()
# b = input()
# print(b[0] + b[1] + a[2] + a[3])
# print(a[0] + a[1] + b[2] + b[3])

# 009:字符串中的整数求和
# w1,w2 = input().split()
# num1 = int(w1[:2])
# num2 = int(w2[:2])
# print(num1 + num2)
#############

### python第二章测试题

# 010:计算2的幂
n = int(input())
print(2 ** n)








