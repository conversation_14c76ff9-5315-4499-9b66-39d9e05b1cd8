# 010:计算2的幂
# n = int(input())
# print(2 ** n)

# 011:计算多项式的值
# x,a,b,c,d = map(float, input().split())    # 通过input().split()获取用户输入的以空格分隔的多个值，然后使用map函数将这些值都转换为浮点数类型，最后将这些浮点数分别赋值给变量x、a、b、c、d。
# result = a * x**3 + b * x**2 + c * x +d    # 计算一个三次多项式的值，该多项式为a * x的三次方 + b * x的二次方 + c * x + d，并将结果赋值给变量result。
# print("{0:.7f}".format(result))            # 使用格式化输出，将result的值以保留 7 位小数的浮点数形式打印输出。

# # 012:奇偶数判断
# n = int(input())
# if n % 2:
#    print("odd")
# else:
#     print("even")

# 013:点和正方形的关系
# x,y = map(int, input().split())         # 读取用户输入的坐标值，并将其转换为整数类型
# if -1 <= x <= 1 and -1 <= y <= 1:       # 判断点是否在正方形内部或边界上,正方形的x坐标范围是[-1, 1]，y坐标范围也是[-1, 1]
#     print("YES")                        # 若满足条件，输出"yes"
# else:
#     print("NO")                         # 若不满足条件，输出"no"

# 014:三角形判断
# x = input().split()
# a,b,c = int(x[0]),int(x[1]),int(x[2])
# if a + b > c and a + c > b and b + c > a:
#     print("Yes")
# else:
#     print("No")

# 015:计算邮资
weight,express= input().split()
weight = int(weight)
fee = 8
if weight > 1000:
    express = weight - 1000
    express_fee = ((express + 499) // 500) * 4
    fee += express_fee


if express == "y":
    fee += 5
print(fee)

# 读取输入：重量(整数)和是否加急(字符)
# weight, express = input().split()
# weight = int(weight)
#
# # 初始化基本费用
# fee = 8
#
# # 计算超重部分费用
# if weight > 1000:
#     # 超出1000克的部分
#     excess = weight - 1000
#     # 不足500克按500克计算，使用向上取整
#     excess_fee = ((excess + 499) // 500) * 4
#     fee += excess_fee
#
# # 加急费用
# if express == 'y':
#     fee += 5
#
# # 输出总费用
# print(fee)